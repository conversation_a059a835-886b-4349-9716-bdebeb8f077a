import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import VinDetailsComponent from './VinDetailsComponent';
import { AppStrings, ExceptionStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { VehicleService } from '../../../../utils/services/VehicleService';
import { UserService } from '../../../../utils/services/UserService';
import { VIN_URL } from '../../../../constants/constants';


interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

interface LoadVinScreenProps {
  vinData: { vinNumber: string; vehicleInfo: VehicleInfo };
  setVinData: (data: { vinNumber: string; vehicleInfo: VehicleInfo }) => void;
  onContinue: () => void;
}

const LoadVinScreen: React.FC<LoadVinScreenProps> = ({ vinData, setVinData, onContinue }) => {
  const [vinNumber, setVinNumber] = useState(vinData.vinNumber || '');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const searchVIN = async (vin: string) => {
    const FORMAT = '?format=json';
    try {
      const response = await fetch(VIN_URL + vin + FORMAT);
      const data = await response.json();
      return data;
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch VIN data');
      return null;
    }
  };

  const processVIN = (vinApiData: any, vin: string) => {
    let vinInfo = {
      vin: vin,
      make: '',
      model: '',
      manufactureYear: '',
      fuel: '',
      warning: '',
      error: '',
      valid: false,
    };

    for (let vinIndex = 0; vinIndex < vinApiData.Count; vinIndex++) {
      switch (vinApiData.Results[vinIndex].VariableId) {
        case 26:
          vinInfo.make = vinApiData.Results[vinIndex].Value;
          break;
        case 28:
          vinInfo.model = vinApiData.Results[vinIndex].Value;
          break;
        case 29:
          vinInfo.manufactureYear = vinApiData.Results[vinIndex].Value;
          break;
        case 24:
          vinInfo.fuel = vinApiData.Results[vinIndex].Value;
          break;
        case 143:
          vinInfo.warning = vinApiData.Results[vinIndex].Value;
          break;
        case 156:
          vinInfo.error = vinApiData.Results[vinIndex].Value
            ? 'The Model Year decoded for this VIN may be incorrect'
            : '';
          break;
        default:
          break;
      }
    }

    if (!vinInfo.error) {
      if (!vinInfo.make) {
        vinInfo.error = 'The Model Year decoded for this VIN may be incorrect';
        vinInfo.valid = false;
        return vinInfo;
      }
      vinInfo.valid = true;
      return vinInfo;
    } else {
      vinInfo.valid = false;
      return vinInfo;
    }
  };

  const loadVIN = async () => {
    if (!vinNumber.trim() || vinNumber.length !== 17) {
      Alert.alert(ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL, ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL);
      return;
    }
    setLoading(true);
    const vinApiData = await searchVIN(vinNumber);
    if (vinApiData) {
      const processedVin = processVIN(vinApiData, vinNumber);
      setVinData({
        vinNumber,
        vehicleInfo: processedVin,
      });
      if (!processedVin.valid) {
        Alert.alert('VIN Error', processedVin.error || 'Invalid VIN data');
      } else {
        Alert.alert('Success', 'VIN loaded successfully');
      }
    }
    setLoading(false);
  };

  const handleContinueWithVehicle = async () => {
    if (!vinData.vehicleInfo.valid) {
      Alert.alert(ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL, ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL);
      return;
    }

    setSaving(true);
    try {
      // Get the stored user ID
      const userId = await AsyncStorage.getItem('userId');
      if (!userId) {
        throw new Error('User ID not found. Please start registration from step 1.');
      }

      // Save vehicle information to Firebase
      await VehicleService.addVehicleInfo(userId, {
        vin: vinData.vehicleInfo.vin,
        make: vinData.vehicleInfo.make,
        model: vinData.vehicleInfo.model,
        manufactureYear: vinData.vehicleInfo.manufactureYear,
        fuel: vinData.vehicleInfo.fuel,
        warning: vinData.vehicleInfo.warning,
      });

      // Update registration status
      await UserService.updateRegistrationStatus(userId, 'registration_completed');

      Alert.alert('Success', 'Vehicle information saved successfully!', [
        { text: 'OK', onPress: () => onContinue() },
      ]);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save vehicle information');
    } finally {
      setSaving(false);
    }
  };

  const handleSkipVehicle = async () => {
    setSaving(true);
    try {
      // Get the stored user ID
      const userId = await AsyncStorage.getItem('userId');
      if (userId) {
        // Update registration status to completed even without vehicle
        await UserService.updateRegistrationStatus(userId, 'registration_completed_no_vehicle');
      }

      Alert.alert('Registration Complete', 'Registration completed successfully!', [
        { text: 'OK', onPress: () => onContinue() },
      ]);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to complete registration');
    } finally {
      setSaving(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.vehicleSection}>
        <RegistrationTitleSection
          title={AppStrings.MCX_MY_VEHICLE_TITLE}
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />
        <View style={styles.inputMainContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{AppStrings.MCX_VIN_LABEL}</Text>
            <CommonTextInput
              value={vinNumber}
              onChangeText={setVinNumber}
              placeholder="Enter VIN number"
              style={styles.vinInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              maxLength={17}
            />
          </View>
          <CustomButton
            text={loading ? 'Loading...' : AppStrings.MCX_LOAD_VIN_BUTTON}
            onPress={loadVIN}
            variant="primary"
            size="small"
            backgroundColor={Colors.PRIMARY}
            textColor="#fff"
            style={styles.loadVinButton}
            isBoldText={true}
            disabled={loading}
          />
        </View>
        {vinData.vehicleInfo.valid && (
          <VinDetailsComponent
            warning={vinData.vehicleInfo.warning}
            vin={vinData.vehicleInfo.vin}
            make={vinData.vehicleInfo.make}
            model={vinData.vehicleInfo.model}
            manufactureYear={vinData.vehicleInfo.manufactureYear}
            fuel={vinData.vehicleInfo.fuel}
          />
        )}
      </View>
      <View style={styles.bottomContainer}>
        <View style={styles.buttonContainer}>
          <CustomButton
            text={saving ? 'Saving...' : 'Skip Now'}
            onPress={handleSkipVehicle}
            variant="outline"
            size="large"
            backgroundColor="#FFFFFF"
            textColor={Colors.SECONDARY}
            isBoldText={true}
            style={styles.skipButton}
            disabled={loading || saving}
          />
          <CustomButton
            text={saving ? 'Saving...' : AppStrings.MCX_CONTINUE_BUTTON}
            onPress={handleContinueWithVehicle}
            variant="primary"
            size="large"
            fullWidth={true}
            backgroundColor={Colors.SECONDARY}
            textColor="#fff"
            isBoldText={true}
            isBottomButton={true}
            bottomLineWidth={1}
            bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
            disabled={loading || saving || !vinData.vehicleInfo.valid}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 10,
    paddingHorizontal: 12,
  },
  skipButton: {
    borderWidth: 1,
    borderColor: Colors.SECONDARY,
    marginBottom: 10,
  },
  vehicleSection: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  inputContainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  vinInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#FFFFFF',
  },
  loadVinButton: {
    borderRadius: 2,
    paddingVertical: 10,
    minHeight: 'auto',
    marginTop: 14,
    marginBottom: 10,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  inputMainContainer: {
    paddingHorizontal: 0,
  },
});

export default LoadVinScreen;
