import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';

interface VinDetailsComponentProps {
  warning: string;
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
}

const VinDetailsComponent: React.FC<VinDetailsComponentProps> = ({
  warning,
  vin,
  make,
  model,
  manufactureYear,
  fuel,
}) => {
  return (
    <View style={styles.container}>
      {warning ? (
        <View style={styles.warningContainer}>
          <Text style={styles.warningText}>{warning}</Text>
        </View>
      ) : null}
      <View style={styles.detailRow}>
        <Text style={styles.label}>VIN:</Text>
        <Text style={styles.value}>{vin}</Text>
      </View>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Make:</Text>
        <Text style={styles.value}>{make}</Text>
      </View>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Model:</Text>
        <Text style={styles.value}>{model}</Text>
      </View>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Year:</Text>
        <Text style={styles.value}>{manufactureYear}</Text>
      </View>
      <View style={styles.detailRow}>
        <Text style={styles.label}>Fuel:</Text>
        <Text style={styles.value}>{fuel}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    marginHorizontal: 12,
    borderRadius: 2,
    padding: 20,
    marginTop: 10,
  },
  warningContainer: {
    backgroundColor: '#FFF3CD',
    borderColor: '#FFECB5',
    borderWidth: 1,
    borderRadius: 4,
    padding: 10,
    marginBottom: 10,
  },
  warningText: {
    color: '#856404',
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  label: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  value: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
  },
});

export default VinDetailsComponent;
