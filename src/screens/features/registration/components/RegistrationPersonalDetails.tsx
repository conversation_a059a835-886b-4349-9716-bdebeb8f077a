import React from 'react';
import { View, Text, StyleSheet, KeyboardAvoidingView, ScrollView, Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { useAuth } from '../../../../utils/configs/AuthContext';
import { UserService } from '../../../../utils/services/UserService';

interface RegistrationPersonalDetailsProps {
  personalData: { firstName: string; lastName: string; email: string; mobile: string };
  setPersonalData: (data: { firstName: string; lastName: string; email: string; mobile: string }) => void;
  onContinue: () => void;
}

const RegistrationPersonalDetails: React.FC<RegistrationPersonalDetailsProps> = ({ personalData, setPersonalData, onContinue }) => {
  const [errors, setErrors] = React.useState<{ firstName?: string; lastName?: string; email?: string; mobile?: string }>({});
  const [loading, setLoading] = React.useState(false);
  const { signUp } = useAuth();

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile.replace(/\D/g, '');
    if (cleanMobile.length !== 10) {
      return 'Enter a valid 10-digit mobile number.';
    }
    return '';
  };

  const validateField = (field: string, value: string) => {
    let error = '';
    if (field === 'firstName' && !value.trim()) {
      error = 'Enter first name.';
    } else if (field === 'lastName' && !value.trim()) {
      error = 'Enter last name.';
    } else if (field === 'email') {
      if (!value.trim()) {
        error = 'Enter a email.';
      } else if (!/\S+@\S+\.\S+/.test(value)) {
        error = 'Enter a valid email.';
      }
    } else if (field === 'mobile') {
      if (!value.trim()) {
        error = 'Enter a mobile number.';
      } else {
        error = validateMobile(value);
      }
    }
    setErrors(prev => ({ ...prev, [field]: error }));
  };

  const validate = () => {
    const newErrors: { firstName?: string; lastName?: string; email?: string; mobile?: string } = {};

    if (!personalData.firstName.trim()) {
      newErrors.firstName = 'Enter first name.';
    }

    if (!personalData.lastName.trim()) {
      newErrors.lastName = 'Enter last name.';
    }

    if (!personalData.email.trim()) {
      newErrors.email = 'Enter a email.';
    } else if (!/\S+@\S+\.\S+/.test(personalData.email)) {
      newErrors.email = 'Enter a valid email.';
    }

    if (!personalData.mobile.trim()) {
      newErrors.mobile = 'Enter a mobile number.';
    } else {
      const mobileError = validateMobile(personalData.mobile);
      if (mobileError) {
        newErrors.mobile = mobileError;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleContinue = async () => {
    if (!validate()) {
      return;
    }

    setLoading(true);
    try {
      // Create a temporary password for the user (they will set their real password in step 2)
      const tempPassword = 'TempPass123!';

      // Create user with Firebase Authentication
      const user = await signUp(personalData.email, tempPassword);

      // Save user data to Firebase Realtime Database
      await UserService.updateUserRegistration(
        user.uid,
        personalData.firstName,
        personalData.lastName,
        personalData.email,
        personalData.mobile,
        'email', // provider type
        user.uid, // provider type id
        'step1_completed' // registration status
      );

      // Store the user ID in AsyncStorage for later use
      await AsyncStorage.setItem('userId', user.uid);
      await AsyncStorage.setItem('userEmail', personalData.email);

      Alert.alert('Success', 'Personal information saved successfully!', [
        { text: 'OK', onPress: () => onContinue() },
      ]);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to save personal information');
    } finally {
      setLoading(false);
    }
  };

  const scrollViewRef = React.useRef<ScrollView>(null);

  const handleInputFocus = (fieldName: string) => {
    setTimeout(() => {
      switch(fieldName) {
        case 'mobile':
          scrollViewRef.current?.scrollTo({ y: 400, animated: true });
          break;
        case 'email':
          scrollViewRef.current?.scrollTo({ y: 300, animated: true });
          break;
        case 'lastName':
          scrollViewRef.current?.scrollTo({ y: 150, animated: true });
          break;
        default:
          scrollViewRef.current?.scrollTo({ y: 0, animated: true });
      }
    }, 300);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 80}
    >
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        <View style={styles.formSection}>
          <RegistrationTitleSection
            title="PERSONAL INFORMATION"
            backgroundColor="#FFFFFF"
            borderBottomWidth={1}
            borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
            paddingVertical={16}
            paddingHorizontal={0}
          />

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>FIRST NAME</Text>
            <CommonTextInput
              value={personalData.firstName}
              onChangeText={(text) => { setPersonalData({ ...personalData, firstName: text }); validateField('firstName', text); }}
              placeholder="Enter your first name"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              onFocus={() => handleInputFocus('firstName')}
            />
            {errors.firstName && <Text style={styles.errorText}>{errors.firstName}</Text>}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>LAST NAME</Text>
            <CommonTextInput
              value={personalData.lastName}
              onChangeText={(text) => { setPersonalData({ ...personalData, lastName: text }); validateField('lastName', text); }}
              placeholder="Enter your last name"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              onFocus={() => handleInputFocus('lastName')}
            />
            {errors.lastName && <Text style={styles.errorText}>{errors.lastName}</Text>}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>EMAIL</Text>
            <CommonTextInput
              value={personalData.email}
              onChangeText={(text) => { setPersonalData({ ...personalData, email: text }); validateField('email', text); }}
              placeholder="Enter your email address"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              keyboardType="email-address"
              onFocus={() => handleInputFocus('email')}
            />
            {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>MOBILE NUMBER</Text>
            <CommonTextInput
              value={personalData.mobile}
              onChangeText={(text) => {
                const cleaned = text.replace(/\D/g, '');
                let formatted = cleaned;
                if (cleaned.length >= 6) {
                  formatted = `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
                } else if (cleaned.length >= 3) {
                  formatted = `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
                }
                setPersonalData({ ...personalData, mobile: formatted });
                validateField('mobile', formatted);
              }}
              placeholder="(*************"
              style={styles.textInput}
              placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
              keyboardType="phone-pad"
              maxLength={14}
              onFocus={() => handleInputFocus('mobile')}
            />
            {errors.mobile && <Text style={styles.errorText}>{errors.mobile}</Text>}
          </View>
        </View>

        {/* Add some bottom padding to ensure button is visible */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      <View style={styles.bottomContainer}>
        <CustomButton
          text={loading ? 'Saving...' : AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          disabled={loading}
        />
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingHorizontal: 0,
    paddingBottom: Platform.OS === 'ios' ? 50 : 30, // Add padding at the bottom for keyboard
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: Sizes.LARGE,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    color: Colors.PRIMARY,
    fontSize: Sizes.SMALL,
    marginTop: 4,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
  },
  bottomSpacing: {
    height: Platform.OS === 'ios' ? 150 : 120, // Increased height to ensure content isn't hidden behind the keyboard
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationPersonalDetails;