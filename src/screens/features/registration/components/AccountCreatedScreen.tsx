import React from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { VehicleService } from '../../../../utils/services/VehicleService';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import CustomButton from '../../../../components/common/CustomButton';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';

interface AccountCreatedScreenProps {
  onContinue: () => void;
}

interface AccountCreatedScreenProps {
  onContinue: () => void;
  vinData?: {
    vinNumber: string;
    vehicleInfo: {
      vin: string;
      make: string;
      model: string;
      manufactureYear: string;
      fuel: string;
      warning?: string;
      valid?: boolean;
    };
  };
  userId?: string;
}

const AccountCreatedScreen: React.FC<AccountCreatedScreenProps> = ({ onContinue, vinData }) => {
  const handleContinue = async () => {
    try {
      const userId = await AsyncStorage.getItem('userId');
      if (vinData?.vehicleInfo.valid && userId) {
        const { vehicleInfo } = vinData;
        await VehicleService.addVehicleInfo(userId, {
          vin: vehicleInfo.vin,
          make: vehicleInfo.make,
          model: vehicleInfo.model,
          manufactureYear: vehicleInfo.manufactureYear,
          fuel: vehicleInfo.fuel,
          warning: vehicleInfo.warning,
        });
      }
      await AsyncStorage.removeItem('userId');
      onContinue();
    } catch (error: any) {
      Alert.alert('Error', 'Failed to save vehicle information. You can add it later from your profile.');
      onContinue();
    } finally {
    }
  };
  return (
    <View style={styles.container}>
      <View style={styles.accountCreatedSection}>
        <RegistrationTitleSection
          title="ACCOUNT CREATED"
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />
        <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer} showsVerticalScrollIndicator={false}>
          <Text style={styles.paragraph}>
            myCANx app for Consumers – a versatile enterprise solution powered by a smartphone app that allows
            mechanics and consumers to interact quickly and effectively. During travel or their daily commute, consumers
            can reach out to mechanics nearest to them, for problems related to their vehicles, wherever they may be!
          </Text>
          <Text style={styles.paragraph}>
            The myCANx app for Consumers is a peer-to-peer marketplace for on-demand mobile mechanics, or skillful auto
            technicians. myCANx provides priceless convenience to consumers, all with the touch of a button. At the
            consumer's request, a mobile mechanic in the myCANx network reaches the consumer's door and takes care of the
            repairs and other services, such as oil change, roadside assistance, fuel delivery, etc.
          </Text>
          <Text style={styles.paragraph}>
            Advantages of myCANx…
          </Text>
          <Text style={styles.paragraph}>
            {'\u00A0\u00A0'} Roadside Assistance
          </Text>
          <Text style={styles.paragraph}>
            myCANx consumers can reach out to myCANx mechanics to take care of their flat tires, fuel deliveries, or jump
            starts.
          </Text>
          <Text style={styles.paragraph}>
            {'\u00A0\u00A0'} Diagnostics and Inspections
          </Text>
          <Text style={styles.paragraph}>
            myCANx mechanics can help myCANx consumers with their pre-purchase inspection for new purchases or leases on a
            vehicle. The mechanics can also check engine lights, fluids and filters, wipers, and more.
          </Text>
          <Text style={styles.paragraph}>
            {'\u00A0\u00A0'} Repair
          </Text>
          <Text style={styles.paragraph}>
            myCANx consumers can book a service appointment and each vehicle will be inspected, diagnosed, and handled
            with care by the trusted and experienced myCANx technicians. Everything from out of tune belts, brakes to hoses
            will be taken care of.
          </Text>
        </ScrollView>
      </View>
      <View style={styles.bottomContainer}>
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  accountCreatedSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    flex: 1,
    overflow: 'hidden',
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 80,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  paragraph: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 24,
    marginBottom: 15,
  },
});

export default AccountCreatedScreen;
