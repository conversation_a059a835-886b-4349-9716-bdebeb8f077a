import React from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { AppStrings } from '../../../../utils/constants/AppStrings';
import { UserService } from '../../../../utils/services/UserService';

interface RegistrationPasswordScreenProps {
  personalData: { firstName: string; lastName: string; email: string; mobile: string };
  passwordData: { password: string; confirmPassword: string };
  setPasswordData: (data: { password: string; confirmPassword: string }) => void;
  setActiveTab: (tab: string) => void;
}

const RegistrationPasswordScreen: React.FC<RegistrationPasswordScreenProps> = ({
  personalData: _personalData,
  passwordData,
  setPasswordData,
  setActiveTab,
}) => {
  const [errors, setErrors] = React.useState<{ password?: string; confirmPassword?: string }>({});
  const [isRegistering, setIsRegistering] = React.useState(false);

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (password.length < minLength) {
      return 'Password must be at least 8 characters.';
    }
    if (!hasUpperCase) {
      return 'Password must contain at least one uppercase letter.';
    }
    if (!hasLowerCase) {
      return 'Password must contain at least one lowercase letter.';
    }
    if (!hasNumbers) {
      return 'Password must contain at least one number.';
    }
    if (!hasSpecialChar) {
      return 'Password must contain at least one special character.';
    }
    return '';
  };

  const handlePasswordChange = (text: string) => {
    const passwordError = validatePassword(text);
    setErrors(prevErrors => ({
      ...prevErrors,
      password: passwordError
    }));
    setPasswordData({ ...passwordData, password: text });
  };

  const handleConfirmPasswordChange = (text: string) => {
    let confirmError = '';
    if (!text) {
      confirmError = 'Confirm your password.';
    } else if (text !== passwordData.password) {
      confirmError = 'Passwords do not match.';
    }
    setErrors(prevErrors => ({
      ...prevErrors,
      confirmPassword: confirmError
    }));
    setPasswordData({ ...passwordData, confirmPassword: text });
  };

  const handleContinue = async () => {
    // Double check all validations
    const passwordError = validatePassword(passwordData.password);
    const confirmError = !passwordData.confirmPassword ? 'Confirm your password.' :
      passwordData.password !== passwordData.confirmPassword ? 'Passwords do not match.' : '';
    
    if (passwordError || confirmError) {
      setErrors({
        password: passwordError,
        confirmPassword: confirmError,
      });
      return;
    }

    setIsRegistering(true);
    try {
      // Get the current user (created in step 1)
      const currentUser = auth().currentUser;
      if (!currentUser) {
        throw new Error('No user found. Please start registration from step 1.');
      }

      // Update the user's password
      await currentUser.updatePassword(passwordData.password);

      // Get the stored user ID
      const userId = await AsyncStorage.getItem('userId');
      if (userId) {
        // Update registration status in database
        await UserService.updateRegistrationStatus(userId, 'step2_completed');
      }

      Alert.alert('Success', 'Password created successfully!', [
        { text: 'OK', onPress: () => setActiveTab('3') },
      ]);
    } catch (error: any) {
      Alert.alert('Registration Error', error.message || 'Failed to register');
    } finally {
      setIsRegistering(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.formSection}>
        <RegistrationTitleSection
          title="CREATE PASSWORD"
          backgroundColor="#FFFFFF"
          borderBottomWidth={1}
          borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
          paddingVertical={16}
          paddingHorizontal={0}
        />

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>PASSWORD</Text>
          <CommonTextInput
            value={passwordData.password}
            onChangeText={handlePasswordChange}
            placeholder="Enter password"
            style={[styles.textInput, errors.password && styles.inputError]}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            secureTextEntry={true}
          />
          {errors.password && <Text style={styles.errorText}>{errors.password}</Text>}
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>CONFIRM PASSWORD</Text>
          <CommonTextInput
            value={passwordData.confirmPassword}
            onChangeText={handleConfirmPasswordChange}
            placeholder="Confirm password"
            style={[styles.textInput, errors.confirmPassword && styles.inputError]}
            placeholderTextColor={Colors.COMMON_GREY_SHADE_LIGHT}
            secureTextEntry={true}
          />
          {errors.confirmPassword && <Text style={styles.errorText}>{errors.confirmPassword}</Text>}
        </View>


      </View>

      <View style={styles.bottomContainer}>
        <CustomButton
          text={isRegistering ? 'Registering...' : AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          backgroundColor={Colors.SECONDARY}
          textColor="#fff"
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          disabled={isRegistering}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 12,
    borderRadius: 2,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: Sizes.LARGE,
    fontWeight: '600',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 20,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  inputLabel: {
    fontSize: Sizes.MEDIUM,
    fontWeight: '500',
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#FFFFFF',
  },
  inputError: {
    borderColor: Colors.PRIMARY,
  },
  errorText: {
    color: Colors.PRIMARY,
    fontSize: Sizes.SMALL,
    marginTop: 4,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  continueButton: {
    borderRadius: 4,
  },
});

export default RegistrationPasswordScreen;
