import React, { useState } from 'react';
import { View, StyleSheet, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CustomTab from '../../../components/common/CustomTabs';
import AppBar from '../../../components/common/AppBar';
import { registrationTabData } from '../../../utils/templates/TemplateConfig';
import { AppStrings } from '../../../utils/constants/AppStrings';
import { Colors } from '../../../utils/constants/Theme';
import RegistrationPersonalDetails from './components/RegistrationPersonalDetails';
import RegistrationCreatePassword from './components/RegistrationCreatePassword';
import LoadVinScreen from './components/LoadVinScreen';
import AccountCreatedScreen from './components/AccountCreatedScreen';

type TabType = keyof typeof registrationTabData;

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

const AccountRegistration = () => {
  const navigation = useNavigation();
  const handleTabChange = (tab: string) => {
    setActiveTab(tab as TabType);
  };
  const [activeTab, setActiveTab] = useState<TabType>('1');

  const [personalData, setPersonalData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    mobile: '',
  });
  const [vinData, setVinData] = useState({
    vinNumber: '',
    vehicleInfo: {} as VehicleInfo,
  });
  const [passwordData, setPasswordData] = useState({
    password: '',
    confirmPassword: '',
  });

  const validateEmail = (email: string) => {
    const re = /\S+@\S+\.\S+/;
    return re.test(email);
  };

  const validateMobile = (mobile: string) => {
    const cleanMobile = mobile.replace(/\D/g, '');
    return cleanMobile.length === 10;
  };

  const isFirstThreeTabsValid = () => {
    const personalValid = personalData.firstName.trim() !== '' &&
      personalData.lastName.trim() !== '' &&
      validateEmail(personalData.email.trim()) &&
      validateMobile(personalData.mobile.trim());

    const passwordValid = passwordData.password.trim().length >= 8 &&
      passwordData.confirmPassword.trim() !== '' &&
      passwordData.password === passwordData.confirmPassword &&
      /[A-Z]/.test(passwordData.password) &&
      /[a-z]/.test(passwordData.password) &&
      /\d/.test(passwordData.password);

    const vinValid = vinData.vehicleInfo.valid === true;

    return personalValid && passwordValid && vinValid;
  };

  const handleTabPress = (tab: TabType) => {
    if (tab === '4') {
      if (!isFirstThreeTabsValid()) {
        Alert.alert(
          'Incomplete Information',
          'Please complete all required information in the first three steps before proceeding.',
        );
        return;
      }
    }
    setActiveTab(tab);
  };

  const handleBackPress = () => {
    navigation.navigate('LoginMainScreen' as never);
  };

  const renderTabs = () => {
    return (
      <View style={styles.tabsContainer}>
        {Object.entries(registrationTabData).map(([key, label]) => (
          <CustomTab
            key={key}
            label={label}
            active={activeTab === key}
            onPress={() => handleTabPress(key as TabType)}
            disabled={key === '4' && !isFirstThreeTabsValid()}
          />
        ))}
      </View>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case '1':
        return <RegistrationPersonalDetails personalData={personalData} setPersonalData={setPersonalData} onContinue={() => setActiveTab('2')} />;
      case '2':
        return (
          <RegistrationCreatePassword
            personalData={personalData}
            passwordData={passwordData}
            setPasswordData={setPasswordData}
            setActiveTab={handleTabChange}
          />
        );
      case '3':
        return <LoadVinScreen vinData={vinData} setVinData={setVinData} onContinue={() => setActiveTab('4')} />;
      case '4':
        return (
          <AccountCreatedScreen
            onContinue={() => navigation.navigate('AppLoginPageScreen' as never)}
            vinData={vinData}
            userId={personalData.email} // Using email as userId since it's unique
          />
        );
      default:
        return <RegistrationPersonalDetails personalData={personalData} setPersonalData={setPersonalData} onContinue={() => setActiveTab('2')} />;
    }
  };

  return (
    <View style={styles.mainContainer}>
      {/* Custom AppBar */}
      <AppBar
        showBackButton={true}
        showMailIcon={false}
        showChatIcon={false}
        showMenuIcon={false}
        showLogo={true}
        onBackPress={handleBackPress}
      />

      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}
      >
        {/* Title Section */}
        <TitleSection
          title={AppStrings.MCX_ACCOUNT_REGISTRATION_TITLE}
          bgColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.titleSection}
        />
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 40 : 0}
        >
          <View style={styles.container}>
            {renderTabs()}
            <View style={styles.contentContainer}>
              {renderTabContent()}
            </View>
          </View>
        </KeyboardAvoidingView>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 20,
  },
  contentContainer: {
    backgroundColor: 'transparent',
    flex: 1,
  },
});

export default AccountRegistration;
