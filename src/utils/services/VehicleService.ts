import database from '@react-native-firebase/database';

interface VehicleData {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning?: string;
}

export const VehicleService = {
  /**
   * Add vehicle information to the customer's profile
   * @param customerId customer id
   * @param vehicleData vehicle information
   * @returns Promise with the reference to the new vehicle data
   */
  addVehicleInfo: (customerId: string, vehicleData: VehicleData) => {
    return database()
      .ref('customer')
      .child(customerId)
      .child('myvehicles')
      .push(vehicleData);
  },

  /**
   * Remove vehicle information from the customer's profile
   * @param customerId customer id
   * @param vehicleId vehicle id
   * @returns Promise that resolves when the vehicle is removed
   */
  removeVehicleInfo: (customerId: string, vehicleId: string) => {
    return database()
      .ref('customer')
      .child(customerId)
      .child('myvehicles')
      .child(vehicleId)
      .remove();
  }
};
