import auth from '@react-native-firebase/auth';
import database from '@react-native-firebase/database';

interface UserRegistrationData {
  email: string;
  firstName: string;
  lastName: string;
  mobile: string;
  providerType: string;
  providerTypeId: string;
  registrationStatus: string;
}

export const UserService = {
  /**
   * Register a new user with email and password and save their details
   */
  registerUser: async (email: string, password: string, userData: Omit<UserRegistrationData, 'email'>) => {
    try {
      // Create user with Firebase Authentication
      const userCredential = await auth().createUserWithEmailAndPassword(email, password);
      const uid = userCredential.user.uid;

      // Save additional user data to Realtime Database
      await database()
        .ref('customer')
        .child(uid)
        .set({
          'email': email,
          'first-name': userData.firstName,
          'last-name': userData.lastName,
          'mobile': userData.mobile,
          'provider-type': userData.providerType,
          'provider-type-id': userData.providerTypeId,
          'registration-status': userData.registrationStatus,
        });

      return uid;
    } catch (error: any) {
      throw new Error(error.message || 'Failed to register user');
    }
  },

  /**
   * Update user registration details in the database
   */
  updateUserRegistration: async (
    customerId: string,
    firstname: string,
    lastname: string,
    email: string,
    phoneNumber: string,
    providerType: string,
    providerTypeId: string,
    registrationStatus: string,
  ) => {
    return database()
      .ref('customer')
      .child(customerId)
      .set({
        'email': email,
        'first-name': firstname,
        'last-name': lastname,
        'mobile': phoneNumber,
        'provider-type': providerType,
        'provider-type-id': providerTypeId,
        'registration-status': registrationStatus,
      });
  },

  /**
   * Update only the registration status
   */
  updateRegistrationStatus: async (customerId: string, registrationStatus: string) => {
    return database()
      .ref('customer')
      .child(customerId)
      .child('registration-status')
      .set(registrationStatus);
  },
};
